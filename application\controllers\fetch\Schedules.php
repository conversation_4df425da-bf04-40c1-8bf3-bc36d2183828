<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedules extends MYT_Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();

        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }

        $this->load->helper('datatables_ssp');
    }

    /**
     * Fetch Schedules
     */
    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => 3307
        ];
        $table = 'schedule';
        $primary_key = 'schedule.id';

        $columns = [
            $this->_get_id(),
            $this->_get_class(),
            $this->_get_customer_name(),
            $this->_get_waf_rs_no(),
            $this->_get_eta_date(),
            $this->_get_trip_no(),
            $this->_get_eta_time(),
            $this->_get_actions()
        ];

        $joins = <<<EOT
schedule
LEFT JOIN customer ON customer.id = schedule.customer_id
LEFT JOIN schedule_list ON schedule_list.schedule_id = schedule.id
EOT;

        $where = <<<EOT
schedule.is_deleted = 0
EOT;

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joins, $where)
        );
    }

    /**
     * Get ID
     */
    protected function _get_id()
    {
        return [
            'db' => 'schedule.id',
            'dt' => 'DT_RowId',
            'field' => 'id'
        ];
    }

    /**
     * Get class
     */
    protected function _get_class()
    {
        return [
            'db' => 'schedule.id',
            'as' => 'class',
            'dt' => 'DT_RowClass',
            'field' => 'class',
            'formatter' => function ($d, $row) {
                return 'schedule';
            }
        ];
    }

    /**
     * Get Customer Name
     */
    protected function _get_customer_name()
    {
        return [
            'db' => 'customer.name',
            'dt' => 0,
            'field' => 'customer_name'
        ];
    }

    /**
     * Get WAF/RS No.
     */
    protected function _get_waf_rs_no()
    {
        return [
            'db' => 'schedule.waf_rs_no',
            'dt' => 1,
            'field' => 'waf_rs_no'
        ];
    }

    /**
     * Get ETA Date
     */
    protected function _get_eta_date()
    {
        return [
            'db' => 'schedule.eta_date',
            'dt' => 2,
            'field' => 'eta_date',
            'formatter' => function ($d, $row) {
                return date('M d, Y', strtotime($d));
            }
        ];
    }

    /**
     * Get Trip No.
     */
    protected function _get_trip_no()
    {
        return [
            'db' => 'schedule_list.trip_no',
            'dt' => 3,
            'field' => 'trip_no'
        ];
    }

    /**
     * Get ETA Time
     */
    protected function _get_eta_time()
    {
        return [
            'db' => 'schedule_list.eta_time',
            'dt' => 4,
            'field' => 'eta_time',
            'formatter' => function ($d, $row) {
                return $d ? date('h:i A', strtotime($d)) : '';
            }
        ];
    }

    /**
     * Get Actions
     */
    protected function _get_actions()
    {
        return [
            'db' => 'schedule.id',
            'dt' => 5,
            'field' => 'schedule.id',
            'formatter' => function ($d, $row) {
                return '<a href="' . site_url('schedules/edit/' . $d) . '" class="btn btn-sm btn-warning">Edit</a> ' .
                    '<a href="' . site_url('schedules/delete/' . $d) . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure you want to delete this schedule?\')">Delete</a>';
            }
        ];
    }
}
