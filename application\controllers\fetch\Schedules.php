<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Schedules extends MYT_Controller
{
    public function __construct()
    {
        $this->public_page = true;
        parent::__construct();
        if (!isset($_SESSION['user'])) {
            exit(json_encode([]));
        }
        $this->load->helper('datatables_ssp');
    }

    public function index()
    {
        $db_config = [
            'user' => $this->db->username,
            'pass' => $this->db->password,
            'db' => $this->db->database,
            'host' => $this->db->hostname,
            'port' => 3307
        ];
        $table = 'schedule';
        $primary_key = 'schedule.id';
        $columns = [
            $this->_get_id(),
            $this->_get_customer_name(),
            $this->_get_waf_rs_no(),
            $this->_get_eta_date(),
            $this->_get_eta_time(),
            $this->_get_actions()
        ];

        $joinQuery = "FROM schedule LEFT JOIN customer ON customer.id = schedule.customer_id LEFT JOIN schedule_list ON schedule_list.schedule_id = schedule.id";
        $where = 'schedule.is_deleted = 0';

        echo json_encode(
            SSP::simple($_GET, $db_config, $table, $primary_key, $columns, $joinQuery, $where)
        );
    }

    protected function _get_id()
    {
        return ['db' => 'schedule.id', 'dt' => 0, 'DT_RowId', 'field' => 'schedule.id'];
    }

    protected function _get_customer_name()
    {
        return ['db' => 'customer.name', 'dt' => 1, 'field' => 'customer.name'];
    }

    protected function _get_waf_rs_no()
    {
        return ['db' => 'schedule.waf_rs_no', 'dt' => 2, 'field' => 'schedule.waf_rs_no'];
    }

    protected function _get_eta_date()
    {
        return ['db' => 'schedule.eta_date', 'dt' => 3, 'field' => 'schedule.eta_date'];
    }

    protected function _get_eta_time()
    {
        return ['db' => 'schedule_list.eta_time', 'dt' => 4, 'field' => 'schedule_list.eta_time'];
    }

    protected function _get_actions()
    {
        return [
            'db' => 'schedule.id',
            'dt' => 5,
            'field' => 'schedule.id',
            'formatter' => function ($d, $row) {
                return '<a href="' . site_url('schedules/edit/' . $d) . '" class="btn btn-sm btn-warning">Edit</a> ' .
                    '<a href="' . site_url('schedules/delete/' . $d) . '" class="btn btn-sm btn-danger" onclick="return confirm(\'Are you sure you want to delete this schedule?\')">Delete</a>';
            }
        ];
    }
}