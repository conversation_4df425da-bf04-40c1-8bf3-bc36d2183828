+ function ($) {
	'use strict';

	function createDataTable(selector) {
		var $table = $(selector);
		var cols = $table.find('thead th');

		var dataTable = $table.DataTable({
			lengthChange: true,
			info: false,
			pagingType: 'full_numbers',
			order: [
				[2, 'desc'] // Order by ETA Date descending
			],
			columnDefs: [{
				className: 'dt-action',
				targets: cols.length - 1
			}],
			autoWidth: false,
			processing: true,
			serverSide: true,
			ajax: BASE_URI + 'fetch/' + $table.attr('data-package')
		});

		return dataTable;
	}

	function deleteAndReload(scheduleId, dataTable) {
		$.when(deleteRow(scheduleId)).then(
			function () {
				dataTable.ajax.reload(null, false);
			},
			function (jqXHR) {
				dataTable.ajax.reload(null, false);

				var response = JSON.parse(jqXHR.responseText);
				alertify.error(response.message);
			}
		);
	}

	function deleteRow(scheduleId) {
		return $.ajax({
			url: BASE_URI + 'schedules/delete/' + scheduleId,
			type: 'POST',
			dataType: 'json'
		});
	}

	$(function () {
		var dataTable = createDataTable('.tableA');

		$('.tableA').on('click', '.action-delete', function (event) {
			var cols = $(event.currentTarget).closest('tr.schedule').find('td');
			var name = typeof cols[3] !== 'undefined' ? cols[3].innerHTML : '';

			alertify.confirm(
				'Are you sure you want to delete ' + name + '?',
				function () {
					var scheduleId = event.currentTarget.getAttribute('data-id');
					deleteAndReload(scheduleId, dataTable);
				}
			).setHeader('<em>Delete Schedule</em>');
		});
	});

}(jQuery);